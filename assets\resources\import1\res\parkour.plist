<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>coin0.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,204},{34,36}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{1,0},{34,36}}</string>
                <key>sourceSize</key>
                <string>{36,36}</string>
            </dict>
            <key>coin1.png</key>
            <dict>
                <key>frame</key>
                <string>{{70,198},{30,36}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{3,0},{30,36}}</string>
                <key>sourceSize</key>
                <string>{36,36}</string>
            </dict>
            <key>coin2.png</key>
            <dict>
                <key>frame</key>
                <string>{{228,140},{24,36}}</string>
                <key>offset</key>
                <string>{-1,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{5,0},{24,36}}</string>
                <key>sourceSize</key>
                <string>{36,36}</string>
            </dict>
            <key>coin3.png</key>
            <dict>
                <key>frame</key>
                <string>{{174,133},{14,36}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{11,0},{14,36}}</string>
                <key>sourceSize</key>
                <string>{36,36}</string>
            </dict>
            <key>coin4.png</key>
            <dict>
                <key>frame</key>
                <string>{{118,48},{8,36}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{14,0},{8,36}}</string>
                <key>sourceSize</key>
                <string>{36,36}</string>
            </dict>
            <key>coin5.png</key>
            <dict>
                <key>frame</key>
                <string>{{174,95},{14,36}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{11,0},{14,36}}</string>
                <key>sourceSize</key>
                <string>{36,36}</string>
            </dict>
            <key>coin6.png</key>
            <dict>
                <key>frame</key>
                <string>{{190,140},{24,36}}</string>
                <key>offset</key>
                <string>{-1,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{5,0},{24,36}}</string>
                <key>sourceSize</key>
                <string>{36,36}</string>
            </dict>
            <key>coin7.png</key>
            <dict>
                <key>frame</key>
                <string>{{38,204},{30,36}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{3,0},{30,36}}</string>
                <key>sourceSize</key>
                <string>{36,36}</string>
            </dict>
            <key>hathpace.png</key>
            <dict>
                <key>frame</key>
                <string>{{60,48},{56,40}}</string>
                <key>offset</key>
                <string>{0,1}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,1},{56,40}}</string>
                <key>sourceSize</key>
                <string>{56,44}</string>
            </dict>
            <key>rock.png</key>
            <dict>
                <key>frame</key>
                <string>{{190,51},{56,44}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{56,44}}</string>
                <key>sourceSize</key>
                <string>{56,44}</string>
            </dict>
            <key>runner0.png</key>
            <dict>
                <key>frame</key>
                <string>{{66,2},{62,44}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,6},{62,44}}</string>
                <key>sourceSize</key>
                <string>{62,56}</string>
            </dict>
            <key>runner1.png</key>
            <dict>
                <key>frame</key>
                <string>{{128,55},{38,56}}</string>
                <key>offset</key>
                <string>{4,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{16,0},{38,56}}</string>
                <key>sourceSize</key>
                <string>{62,56}</string>
            </dict>
            <key>runner2.png</key>
            <dict>
                <key>frame</key>
                <string>{{58,124},{32,54}}</string>
                <key>offset</key>
                <string>{5,-1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{20,2},{32,54}}</string>
                <key>sourceSize</key>
                <string>{62,56}</string>
            </dict>
            <key>runner3.png</key>
            <dict>
                <key>frame</key>
                <string>{{118,95},{40,54}}</string>
                <key>offset</key>
                <string>{4,-1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{15,2},{40,54}}</string>
                <key>sourceSize</key>
                <string>{62,56}</string>
            </dict>
            <key>runner4.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,2},{62,44}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,6},{62,44}}</string>
                <key>sourceSize</key>
                <string>{62,56}</string>
            </dict>
            <key>runner5.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,48},{42,56}}</string>
                <key>offset</key>
                <string>{2,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{12,0},{42,56}}</string>
                <key>sourceSize</key>
                <string>{62,56}</string>
            </dict>
            <key>runner6.png</key>
            <dict>
                <key>frame</key>
                <string>{{60,90},{32,56}}</string>
                <key>offset</key>
                <string>{8,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{23,0},{32,56}}</string>
                <key>sourceSize</key>
                <string>{62,56}</string>
            </dict>
            <key>runner7.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,92},{40,54}}</string>
                <key>offset</key>
                <string>{6,-1}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{17,2},{40,54}}</string>
                <key>sourceSize</key>
                <string>{62,56}</string>
            </dict>
            <key>runnerCrouch0.png</key>
            <dict>
                <key>frame</key>
                <string>{{56,158},{39,38}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <false/>
                <key>sourceColorRect</key>
                <string>{{0,0},{39,38}}</string>
                <key>sourceSize</key>
                <string>{39,38}</string>
            </dict>
            <key>runnerJumpDown0.png</key>
            <dict>
                <key>frame</key>
                <string>{{190,97},{41,45}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{41,45}}</string>
                <key>sourceSize</key>
                <string>{41,45}</string>
            </dict>
            <key>runnerJumpDown1.png</key>
            <dict>
                <key>frame</key>
                <string>{{130,2},{51,58}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{51,58}}</string>
                <key>sourceSize</key>
                <string>{51,58}</string>
            </dict>
            <key>runnerJumpUp0.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,134},{30,52}}</string>
                <key>offset</key>
                <string>{8,-2}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{24,4},{30,52}}</string>
                <key>sourceSize</key>
                <string>{62,56}</string>
            </dict>
            <key>runnerJumpUp1.png</key>
            <dict>
                <key>frame</key>
                <string>{{114,137},{34,44}}</string>
                <key>offset</key>
                <string>{5,-6}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{19,12},{34,44}}</string>
                <key>sourceSize</key>
                <string>{62,56}</string>
            </dict>
            <key>runnerJumpUp2.png</key>
            <dict>
                <key>frame</key>
                <string>{{2,166},{36,38}}</string>
                <key>offset</key>
                <string>{3,-9}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{16,18},{36,38}}</string>
                <key>sourceSize</key>
                <string>{62,56}</string>
            </dict>
            <key>runnerJumpUp3.png</key>
            <dict>
                <key>frame</key>
                <string>{{190,2},{47,56}}</string>
                <key>offset</key>
                <string>{0,0}</string>
                <key>rotated</key>
                <true/>
                <key>sourceColorRect</key>
                <string>{{0,0},{47,56}}</string>
                <key>sourceSize</key>
                <string>{47,56}</string>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>2</integer>
            <key>realTextureFileName</key>
            <string>parkour.png</string>
            <key>size</key>
            <string>{256,256}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:019e98482025b3f5822d4a5c70d29d1f:1/1$</string>
            <key>textureFileName</key>
            <string>parkour.png</string>
        </dict>
    </dict>
</plist>
