cc.Class({
    extends: cc.Component,

    properties: {
        backgroundSprites: {
            default: [],
            type: [cc.SpriteFrame],
            tooltip: "背景图片数组"
        },
        scrollSpeed: {
            default: 100,
            tooltip: "背景滚动速度"
        },
        changeInterval: {
            default: 10,
            tooltip: "背景切换间隔时间（秒）"
        },
        enableRandomChange: {
            default: true,
            tooltip: "是否启用随机背景切换"
        },
        overlapPixels: {
            default: 20,
            tooltip: "背景重叠像素数，防止黑边"
        }
    },

    onLoad() {
        // 获取屏幕宽度
        this.screenWidth = cc.winSize.width;
        
        // 当前背景索引
        this.currentBgIndex = 0;
        
        // 背景切换计时器
        this.changeTimer = 0;
        
        // 创建背景节点数组（用于无缝滚动）
        this.backgroundNodes = [];
        
        // 初始化背景
        this.initBackgrounds();
    },

    // 初始化背景系统
    initBackgrounds() {
        // 创建三个背景节点用于更好的无缝滚动
        for (let i = 0; i < 3; i++) {
            let bgNode = new cc.Node(`Background_${i}`);
            let sprite = bgNode.addComponent(cc.Sprite);

            // 设置初始背景图片
            if (this.backgroundSprites.length > 0) {
                sprite.spriteFrame = this.backgroundSprites[this.currentBgIndex];
                // 设置图片填充模式，防止拉伸变形
                sprite.sizeMode = cc.Sprite.SizeMode.CUSTOM;
            }

            // 设置节点属性
            bgNode.parent = this.node;
            bgNode.zIndex = -1; // 确保背景在最底层

            // 设置大小以覆盖整个屏幕，增加重叠像素防止黑边
            let bgWidth = this.screenWidth + this.overlapPixels;
            let bgHeight = cc.winSize.height + this.overlapPixels;
            bgNode.width = bgWidth;
            bgNode.height = bgHeight;

            // 设置位置：确保完全无缝连接，有重叠
            bgNode.x = i * (this.screenWidth - this.overlapPixels / 2) - this.screenWidth;
            bgNode.y = 0;

            this.backgroundNodes.push(bgNode);
        }

        console.log(`背景管理器初始化完成，共有 ${this.backgroundSprites.length} 个背景图片`);
    },

    start() {},

    update(dt) {
        // 滚动背景
        this.scrollBackgrounds(dt);
        
        // 处理背景切换
        if (this.enableRandomChange) {
            this.handleBackgroundChange(dt);
        }
    },

    // 滚动背景
    scrollBackgrounds(dt) {
        this.backgroundNodes.forEach(bgNode => {
            // 向左移动
            bgNode.x -= this.scrollSpeed * dt;

            // 如果背景完全移出屏幕左侧，将其移动到最右侧
            if (bgNode.x <= -this.screenWidth - this.overlapPixels) {
                // 找到最右边的背景节点位置
                let rightmostX = Math.max(...this.backgroundNodes.map(node => node.x));
                // 将当前节点移动到最右边，有重叠防止黑边
                bgNode.x = rightmostX + this.screenWidth - this.overlapPixels / 2;

                // 可选：在这里切换到新的背景图片
                this.updateBackgroundSprite(bgNode);
            }
        });
    },

    // 处理背景切换计时
    handleBackgroundChange(dt) {
        this.changeTimer += dt;
        
        if (this.changeTimer >= this.changeInterval) {
            this.changeToNextBackground();
            this.changeTimer = 0;
        }
    },

    // 切换到下一个背景
    changeToNextBackground() {
        if (this.backgroundSprites.length <= 1) return;
        
        // 随机选择下一个背景（确保不重复当前背景）
        let nextIndex;
        do {
            nextIndex = Math.floor(Math.random() * this.backgroundSprites.length);
        } while (nextIndex === this.currentBgIndex && this.backgroundSprites.length > 1);
        
        this.currentBgIndex = nextIndex;
        
        // 更新所有背景节点的图片
        this.backgroundNodes.forEach(bgNode => {
            this.updateBackgroundSprite(bgNode);
        });
        
        console.log(`背景切换到索引: ${this.currentBgIndex}`);
    },

    // 更新背景节点的图片
    updateBackgroundSprite(bgNode) {
        if (this.backgroundSprites.length > 0) {
            let sprite = bgNode.getComponent(cc.Sprite);
            sprite.spriteFrame = this.backgroundSprites[this.currentBgIndex];
            // 确保图片填充模式正确
            sprite.sizeMode = cc.Sprite.SizeMode.CUSTOM;
            // 重新设置尺寸以防止图片变形，包含重叠像素
            let bgWidth = this.screenWidth + this.overlapPixels;
            let bgHeight = cc.winSize.height + this.overlapPixels;
            bgNode.width = bgWidth;
            bgNode.height = bgHeight;
        }
    },

    // 设置滚动速度（由GameManager调用）
    setScrollSpeed(speed) {
        this.scrollSpeed = speed;
    },

    // 手动切换到指定背景
    changeToBackground(index) {
        if (index >= 0 && index < this.backgroundSprites.length) {
            this.currentBgIndex = index;
            this.backgroundNodes.forEach(bgNode => {
                this.updateBackgroundSprite(bgNode);
            });
            console.log(`手动切换背景到索引: ${index}`);
        }
    },

    // 重置背景系统
    resetBackgrounds() {
        this.currentBgIndex = 0;
        this.changeTimer = 0;

        // 重置背景位置，确保有重叠
        this.backgroundNodes.forEach((bgNode, index) => {
            bgNode.x = index * (this.screenWidth - this.overlapPixels / 2) - this.screenWidth;
            this.updateBackgroundSprite(bgNode);
        });

        console.log("背景系统已重置");
    },

    // 暂停/恢复背景切换
    setBackgroundChangeEnabled(enabled) {
        this.enableRandomChange = enabled;
    }
});
