cc.Class({
    extends: cc.Component,

    properties: {
        // 跳跃按钮
        jumpButton: {
            default: null,
            type: cc.Button
        },
        
        // 滑行按钮
        slideButton: {
            default: null,
            type: cc.Button
        },
        
        // player节点引用
        playerNode: {
            default: null,
            type: cc.Node
        }
    },

    onLoad() {
        // 绑定按钮点击事件
        this.setupButtonEvents();
    },

    setupButtonEvents() {
        // 绑定跳跃按钮点击事件
        if (this.jumpButton) {
            this.jumpButton.node.on('click', this.onJumpButtonClick, this);
        }
        
        // 绑定滑行按钮点击事件
        if (this.slideButton) {
            this.slideButton.node.on('click', this.onSlideButtonClick, this);
        }
    },

    onJumpButtonClick() {
        console.log("跳跃按钮被点击");
        
        if (this.playerNode) {
            // 获取player的脚本组件
            let playerScript = this.playerNode.getComponent('Player');
            if (playerScript && playerScript.jump) {
                playerScript.jump();
            } else {
                console.warn("player节点上没有找到Player脚本组件或jump方法");
            }
        } else {
            console.warn("playerNode引用为空");
        }
    },

    onSlideButtonClick() {
        console.log("滑行按钮被点击");
        
        if (this.playerNode) {
            // 获取player的脚本组件
            let playerScript = this.playerNode.getComponent('Player');
            if (playerScript && playerScript.slide) {
                playerScript.slide();
            } else {
                console.warn("player节点上没有找到Player脚本组件或slide方法");
            }
        } else {
            console.warn("playerNode引用为空");
        }
    },

    onDestroy() {
        // 移除按钮事件监听
        if (this.jumpButton) {
            this.jumpButton.node.off('click', this.onJumpButtonClick, this);
        }
        
        if (this.slideButton) {
            this.slideButton.node.off('click', this.onSlideButtonClick, this);
        }
    }
});
