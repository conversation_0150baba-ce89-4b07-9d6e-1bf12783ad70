第一步：重写PlatformManager基础结构
目标：建立新的PlatformManager框架
内容：

定义预制体引用属性
设置基础配置（移动速度、间距等）
创建基本的初始化方法
🎯 第一步：实现预制体地面管理
目标：管理动态生成的地面
内容：

实例化预制体地面
实现移动逻辑
🎯 第二步：实现过渡检测逻辑
目标：知道何时开始生成预制体
内容：

检测预制体地面是否快用完
设置切换时机
🎯 第三步：实现预制体生成系统
目标：动态创建新地面
内容：

实例化预制体
设置生成规则（安全vs挑战）
管理生成位置
🎯 第二步：实现预设地面管理
目标：管理场景中现有的Ground节点
内容：

获取场景中的Ground1-4节点
记录它们的初始状态
实现移动逻辑
🎯 第三步：实现过渡检测逻辑
目标：知道何时开始生成预制体
内容：

检测预设地面是否快用完
设置切换时机
🎯 第四步：实现预制体生成系统
目标：动态创建新地面
内容：

实例化预制体
设置生成规则（安全vs挑战）
管理生成位置
🎯 第五步：实现回收机制
目标：优化内存使用
内容：

预设地面：移出后销毁
预制体地面：移出后回收重用